import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import robotPng from "../../../public/robot.png";

const Frame = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const kgtoken = new URLSearchParams(location.search).get('kgtoken');

    const toRAG = () => {
        if (location.pathname === '/dataA/rag') {
            navigate(`/dataA?kgtoken=${kgtoken}`); // 返回上级路由
        } else {
            navigate(`/dataA/rag?kgtoken=${kgtoken}`); // 保持原有跳转
        }
    }

    return (
        <div className='flex-column_container'>
            <div className='top_bar_container shrink-0 '>
                <img className='top_bar_logo margin-row' src="unicom.png" alt="" />
                <span className='top_bar_title'>网络数据分析助手</span>


                <button className='top_bar_rag' onClick={toRAG}>
                    {location.pathname === '/dataA/rag' ? '返回问答' : '添加知识库'}
                </button>
                <img className='top_bar_user_avatar' src={robotPng} alt="" />
                <span className='top_bar_user_name margin-row'>用户</span>

            </div>

            <div className='flex-auto_container'>
                <Outlet></Outlet>
            </div>

        </div >
    )
}
export default Frame;
import { useEffect } from "react";
import { useRoutes, useLocation, useMatch } from 'react-router-dom';
import routes from '../config/router.config';

const App = () => {
    const route = useRoutes(routes)
    const location  = useLocation();

    useEffect(() => {
        // console.log('location-->', useMatch(pathname));
        console.log('location-->', location);
    }, [location ])
    return <>{route}</>
}
export default App;
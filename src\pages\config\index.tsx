import {
    <PERSON><PERSON>ble,
    Sender,
    useXA<PERSON>,
    use<PERSON><PERSON><PERSON>,
    XRequest,
    Attachments
} from '@ant-design/x';
import useStyle from './styles';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { type GetProp, Select, Button, GetRef, Flex, Image, Card, Input, InputNumber } from 'antd';
import { ReactFlow, Background, Controls, MiniMap, addEdge } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { LinkOutlined, SyncOutlined } from "@ant-design/icons";
import Robot from '@/assets/robot.png';
import { ModelData } from "./response";
import AiResult, { type AIResultContent } from "./components/aiResult";
import Placeholder, { assistanModeEnum, RichAssistantModeEnum } from "./components/placeholder";
import UploadFile from "./components/uploadFile";
import './css.css'
import SelectButton from './components/selectbutton'
import { setRawMessageList } from "./components/aiResult/tools"
import { v4 as uuidv4 } from 'uuid';
import { read, utils } from 'xlsx';
import ExcelParser from './components/excelParser';
// Base API URL
const API_BASE_URL = import.meta.env.VITE_APP_API_URL;

const MODEL_MODE = {
    LLMS: 'LLMS',
    MMLLMS: 'MMLLMS',
}

// 定义模型源配置类型
type ModelSourceConfig = {
    id: string;
    name: string;
    baseURL: string;
    headers: Record<string, string>;
    formatRequest: (requestBody: any, modelMode: string, message: any, brandEqpPrompt: string, excelPrompt: string, isInternetSearch: boolean) => any;
    parseResponse: (data: string, searchResults: any[]) => { content: string; searchResults: any[] };
};

// 定义模型类型
type ModelType = {
    id: string;
    name: string;
    source: 'out' | 'inner';
    avaliable: boolean;
};

// 模型列表
const MODELS: ModelType[] = [
    // { id: 'qwen-plus', name: 'Qwen Plus', source: 'out' },
    { id: 'Qwen2.5-72B-Instruct', name: 'Qwen2.5-72B-Instruct', source: 'inner', avaliable: true },
    { id: 'ChinaUnicom-Net-LLM-34B-Chat', name: 'ChinaUnicom-Net-LLM-34B-Chat', source: 'inner', avaliable: true },
    { id: 'DeepSeek-R1', name: 'DeepSeek-R1', source: 'inner', avaliable: false },
    { id: 'DeepSeek-R1-Distill-Qwen-32B', name: 'DeepSeek-R1-Distill-Qwen-32B', source: 'inner', avaliable: true },
    { id: 'Qwen2.5-7B-Instruct', name: 'Qwen2.5-7B-Instruct', source: 'inner', avaliable: true },
    { id: 'Qwen2.5-3B-Instruct', name: 'Qwen2.5-3B-Instruct', source: 'inner', avaliable: true },
    { id: 'Qwen2.5-1.5B-Instruct', name: 'Qwen2.5-1.5B-Instruct', source: 'inner', avaliable: true },
    { id: 'QwQ-32B', name: 'QwQ-32B', source: 'inner', avaliable: true },
    { id: 'deepseek_v3_int8_vpc', name: 'deepseek_v3_int8_vpc', source: 'inner', avaliable: true },
    { id: 'ChinaUnicom-Net-LLM-34B-Chat-v3', name: 'ChinaUnicom-Net-LLM-34B-Chat-v3', source: 'inner', avaliable: false },
];

// 模型源配置
const MODEL_SOURCES: Record<string, ModelSourceConfig> = {
    OUT: {
        id: 'out',
        name: '外部模型',
        baseURL: API_BASE_URL + "/config-bk/api",
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'X-DashScope-SSE': 'enable',
            'Authorization': 'Bearer sk-a05ff16ef8414a45a75db780ecd11d67'
        },
        formatRequest: (requestBody: any, modelMode: string, message: any, brandEqpPrompt: string, excelPrompt: string, isInternetSearch: boolean) => {
            if (modelMode === MODEL_MODE.LLMS) {
                return {
                    ...requestBody,
                    model: "qwen-plus",
                    q: [
                        { "role": "system", "content": brandEqpPrompt + excelPrompt },
                        { "role": "user", "content": message.content.question + JSON.stringify((message?.content as RequestInfo).files[0]?.data || {}) ? '\n\n附件的配置:' + JSON.stringify((message?.content as RequestInfo).files[0]?.data || {}) : '' + (message?.system ?? "") }
                    ],
                    type: "dashscope",
                    enable_search: isInternetSearch,
                    search_options: {
                        "enable_source": isInternetSearch,
                        "enable_citation": isInternetSearch
                    }
                };
            } else if (modelMode === MODEL_MODE.MMLLMS) {
                const urls: {}[] = [];
                (message && message.content as RequestInfo)?.files?.forEach((img: any) => {
                    urls.push({
                        type: "image_url",
                        image_url: {
                            url: img.data
                        }
                    });
                });

                return {
                    ...requestBody,
                    model: "qwen-vl-max-latest",
                    q: [{
                        role: "system", content: [{ type: "text", text: brandEqpPrompt }]
                    },
                    {
                        role: "user", content: [...urls, { type: "text", text: message && (message.content as RequestInfo).question }]
                    }],
                };
            }
            return requestBody;
        },
        parseResponse: (data: string, searchResults: any[]) => {

            let content = '';

            let msgData = JSON.parse(data);


            if (msgData.output?.choices?.[0]?.message?.content) {
                content = msgData.output.choices[0].message.content;
            }

            const updatedSearchResults = searchResults?.length === 0 && msgData.output?.search_info?.search_results
                ? msgData.output.search_info.search_results
                : searchResults;


            return { content, searchResults: updatedSearchResults };
        }
    },
    INNER: {
        id: 'inner',
        name: '内部模型',
        baseURL: API_BASE_URL + '/config-rag' + "/CUCCAI-llm-hub/chat/completions",
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Authorization': 'Bearer 618149eb-d43e-4ddc-b406-b0c0e1efd281'
        },
        formatRequest: (requestBody: any, modelMode: string, message: any, brandEqpPrompt: string, excelPrompt: string, isInternetSearch: boolean) => {
            if (modelMode === MODEL_MODE.LLMS) {
                return {
                    ...requestBody,
                    model: requestBody.model || "Qwen2.5-72B-Instruct", // 使用传入的模型ID或默认值
                    messages: [
                        { "role": "system", "content": brandEqpPrompt + excelPrompt },
                        { "role": "user", "content": message.content.question + ((message?.content as RequestInfo).files[0]?.data ? (+'\n\n附件的配置:' + JSON.stringify((message?.content as RequestInfo).files[0]?.data) || '') : '') + (message?.system ?? "") }
                    ],
                    stream: true
                };
            } else if (modelMode === MODEL_MODE.MMLLMS) {
                // 内部多模态模型使用外部模型的配置和格式
                const urls: {}[] = [];
                (message && message.content as RequestInfo)?.files?.forEach((img: any) => {
                    urls.push({
                        type: "image_url",
                        image_url: {
                            url: img.data
                        }
                    });
                });

                return {
                    ...requestBody,
                    model: "qwen-vl-max-latest",
                    q: [{
                        role: "system", content: [{ type: "text", text: brandEqpPrompt }]
                    },
                    {
                        role: "user", content: [...urls, { type: "text", text: message && (message.content as RequestInfo).question }]
                    }],
                };
            }
            return requestBody;
        },
        parseResponse: (data: string, searchResults: any[]) => {

            let content = '';
            console.log(data)
            if (data === '[DONE]') {
                return { content, searchResults: [] };
            }
            const msgData = JSON.parse(data);

            if (msgData?.choices?.[0]?.delta?.content) {
                content = msgData.choices[0].delta.content;
            }

            return { content, searchResults };
        }
    }
};

export type FileUpLoadType = {
    fileExtension: string;
    data: any; // 这里使用了xlsx库的utils.sheet_to_json方法来将Excel数据转换为JSO
    type: string;
    fileName: string,
    rawData: any,
}

export type RequestInfo = {
    question: string;
    files: FileUpLoadType[]
}

export type AgentMessage = {
    role: 'local' | 'ai' | 'model' | 'botton';
    system?: string,
    content: string | AIResultContent | ModelData | RequestInfo;
    qaid?: string
    isfavourite: number
};


interface QAProps {
    conversationId: string;
    assistantMode: number | null;
    isActive: boolean;
}

const flowNodes = [{
    id: assistanModeEnum.TOPO_ANAYLSIS,
    data: { label: '拓扑分析' },
    position: { x: 100, y: 100 },
},
{
    id: assistanModeEnum.CONFIG_RECOMMEND,
    data: { label: '配置推荐' },
    position: { x: 100, y: 100 },
},
{
    id: assistanModeEnum.CONFIG_GENERATOR,
    data: { label: '配置生成' },
    position: { x: 100, y: 100 },
},
{
    id: assistanModeEnum.ASSISTANT_DEBUG,
    data: { label: '辅助调测' },
    position: { x: 100, y: 100 },
},
{
    id: assistanModeEnum.NETWORK_SIMULATOR,
    data: { label: '模拟仿真' },
    position: { x: 100, y: 100 },
}
]

// 用于调试
// const print = (txt: any) => {
//     console.log(txt)
// }


const Independent: React.FC<QAProps> = (QAProps) => {
    const [nodes] = useState(() =>
        flowNodes.map((item, index) => ({
            id: item.id,
            data: { label: item.data.label },
            position: { x: 100 + index * 300, y: 100 + (index % 2) * 150 },
            type: 'default',
            sourcePosition: () => {
                // if (item.id === assistanModeEnum.TOPO_ANAYLSIS) {
                //     return undefined
                // }
                return 'left'
            },
            targetPosition: item.id === assistanModeEnum.TOPO_ANAYLSIS ? 0 : 'left',
            style: { width: 200, height: 80, padding: '10px', fontSize: '30px', lineHeight: '30px', borderRadius: '10px', backgroundColor: '#dae1ec', border: '0px solid #ccc', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center', },
        }))
    );
    const [edges, setEdges] = useState([
        {
            id: 'topo-to-config',
            source: assistanModeEnum.TOPO_ANAYLSIS,
            target: assistanModeEnum.CONFIG_RECOMMEND,
            // type: 'smoothedge',
            animated: true,
            style: { stroke: '#333' },
            markerEnd: {
                type: 'arrowclosed',
                color: '#333',
                width: 30,
                height: 30,
            },
        },
        {
            id: 'topo-to-generator',
            source: assistanModeEnum.TOPO_ANAYLSIS,
            target: assistanModeEnum.CONFIG_GENERATOR,
            // type: 'smoothedge',
            animated: true,
            style: { stroke: '#333' },
            markerEnd: {
                type: 'arrowclosed',
                color: '#333',
                width: 30,
                height: 30,
            },
        },
        {
            id: 'config-to-generator',
            source: assistanModeEnum.CONFIG_RECOMMEND,
            target: assistanModeEnum.CONFIG_GENERATOR,
            // type: 'smoothedge',
            animated: true,
            style: { stroke: '#333' },
            markerEnd: {
                type: 'arrowclosed',
                color: '#333',
                width: 30,
                height: 30,
            },
        },
        {
            id: 'generator-to-debug',
            source: assistanModeEnum.CONFIG_GENERATOR,
            target: assistanModeEnum.ASSISTANT_DEBUG,
            // type: 'smoothedge',
            animated: true,
            style: { stroke: '#333' },
            markerEnd: {
                type: 'arrowclosed',
                color: '#333',
                width: 30,
                height: 30,
            },
        },
        {
            id: 'debug-to-simulator',
            source: assistanModeEnum.ASSISTANT_DEBUG,
            target: assistanModeEnum.NETWORK_SIMULATOR,
            // type: 'smoothedge',
            animated: true,
            style: { stroke: '#333' },
            markerEnd: {
                type: 'arrowclosed',
                color: '#333',
                width: 30,
                height: 30,
            },
        },
    ]);

    const defaultEdgeOptions = {
        // type: 'smoothedge',
        animated: true,
        style: { stroke: '#333' },
        markerEnd: {
            type: 'arrowclosed',
            color: '#333',
            width: 30,
            height: 30,
        },
    };

    const onConnect = useCallback((params: any) => {
        setEdges((eds) => addEdge(params, eds));
    }, []);

    // let brandEqpPrompt = ''
    // let isInternetSearch = false
    // ==================== Style ====================
    const { styles } = useStyle();

    // ==================== State ====================
    const [content, setContent] = React.useState('');
    const [uploadedFiles, setUploadedFiles] = React.useState<any[]>([]);

    const handleFileUpload = async (files: any[]) => {
        setUploadedFiles(files);
    };

    const handleFile = async (file: File): Promise<any> => {
        const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
        if (['xlsx', 'xls'].includes(fileExtension)) {
            console.log('excel')
            const data = await file.arrayBuffer();
            const workbook = read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            console.log('Excel数据：', utils.sheet_to_json(worksheet))
            return {
                fileExtension: fileExtension,
                data: utils.sheet_to_json(worksheet), // 这里使用了xlsx库的utils.sheet_to_json方法来将Excel数据转换为JSO
                type: 'excel',
                fileName: file.name,
                rawData: file
            } as FileUpLoadType;
        } else if (['jpg', 'jpeg', 'png'].includes(fileExtension)) {
            // 对于图片文件，返回base64
            console.log('image')
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve({
                    data: reader.result as string,
                    fileExtension: fileExtension,
                    type: 'image',
                    fileName: file.name,
                    rawData: file
                } as FileUpLoadType);
                reader.onerror = (error) => reject(error);
            });
        } else {
            return null
        }
    };


    //=====================dlckaddstate==================
    const [chosenBrand, setChosenBrand] = React.useState('不限')
    const [chosenEquipment, setChosenEquipment] = React.useState('不限')
    const [currentAssistantMode, setCurrentAssistantMode] = React.useState('')
    const [selectedModel, setSelectedModel] = React.useState(MODELS[0].id)
    // 使用 useRef 来跟踪当前的模型配置和模型ID，这样可以确保我们总是使用最新的值
    const modelConfigRef = useRef(MODEL_SOURCES.OUT)
    const selectedModelRef = useRef(MODELS[0].id)

    // 添加 topK 和 similarity 的状态和 ref
    const [topK, setTopK] = useState<number>(6)
    const [similarity, setSimilarity] = useState<number>(0.4)
    const topKRef = useRef<number>(6)
    const similarityRef = useRef<number>(0.4)

    // 当选择的模型变化时更新 modelConfigRef 和 selectedModelRef
    useEffect(() => {
        // 根据选择的模型ID找到对应的模型信息
        const modelInfo = MODELS.find(model => model.id === selectedModel);
        if (modelInfo) {
            // 根据模型来源设置对应的配置
            modelConfigRef.current = modelInfo.source === 'out' ? MODEL_SOURCES.OUT : MODEL_SOURCES.INNER;
            selectedModelRef.current = modelInfo.id;
            console.log("模型已切换为:", modelInfo.name, "配置已更新为:", modelConfigRef.current.id);
        }
    }, [selectedModel])

    // 当 topK 和 similarity 变化时更新对应的 ref
    useEffect(() => {
        topKRef.current = topK;
    }, [topK])

    useEffect(() => {
        similarityRef.current = similarity;
    }, [similarity])

    let brandEqpPrompt = useRef('')
    let isInternetSearch = useRef(false)
    const uploadRef = useRef<GetRef<typeof UploadFile>>(null)
    const placeholderRef = useRef<GetRef<typeof Placeholder>>(null)
    let isRefreshList = useRef(true)
    let tempMessageListBeforeDelete = useRef<typeof messages>([]);

    const [mutiAgentSubAssistantModeState, setMutiAgentSubAssistantModeState] = React.useState<string>(assistanModeEnum.TOPO_ANAYLSIS)
    const mutiAgentSubAssistantMode = React.useRef<string>(mutiAgentSubAssistantModeState)
    useEffect(() => {
        mutiAgentSubAssistantMode.current = mutiAgentSubAssistantModeState
        console.log('子助手切换', mutiAgentSubAssistantMode.current)
    }, [mutiAgentSubAssistantModeState])


    const brand = [
        { value: '不限', label: '不限', id: 'allbrand' },
        { value: '华为', label: '华为', id: 'HUAWEI' },
        { value: '华三', label: '华三', id: 'H3C' },
        { value: '中兴', label: '中兴', id: 'ZTE' },
    ]

    const equipment = [
        { value: '不限', label: '不限', id: 'alleqp' },
        { value: '路由器', label: '路由器', id: "router" },
        { value: '交换机', label: '交换机', id: "switch" },
        { value: '防火墙', label: '防火墙', id: "firewall" },
        { value: '负载', label: '负载', id: "loadbalance" }
    ]
    const brandChange = (brand: any) => {
        setChosenBrand(brand)
    }
    const equipmentChange = (equipment: any) => {
        setChosenEquipment(equipment)
    }

    const mutiAgentSubAssistantModeChange = (subAssistantMode: any) => {
        setMutiAgentSubAssistantModeState(subAssistantMode)

    }

    useEffect(() => {
        // console.log('所选设备：' + chosenEquipment, '\n所选厂商:' + chosenBrand)
        brandEqpPrompt.current = getPrompt(chosenEquipment, chosenBrand)
        // console.log('提示词变化：' + brandEqpPrompt)
    }, [chosenEquipment, chosenBrand, currentAssistantMode, mutiAgentSubAssistantModeState])


    useEffect(() => {
        if (QAProps.isActive && currentAssistantMode) {

            const event = new CustomEvent('highlight-assistant', {
                detail: { key: currentAssistantMode }
            });
            setTimeout(() => {
                console.log('切换mod' + currentAssistantMode)
                window.dispatchEvent(event);
            }, 0);

        }
        else {
            console.log('取消高亮')
            window.dispatchEvent(new Event('clear-assistant-highlight'));
        }

    }, [currentAssistantMode, QAProps.isActive])

    const getPrompt = (eqp: string, brand: string) => {
        let finalPrompt = "你的身份是联通的云池数通设备配置助手，你需要帮助用户解答云资源配置的相关问题。"
        if ((currentAssistantMode == assistanModeEnum.CONFIG_RECOMMEND) || (currentAssistantMode == assistanModeEnum.MULTI_AGENT && mutiAgentSubAssistantModeState == assistanModeEnum.CONFIG_RECOMMEND)) {

            if (brand && brand != '不限') {
                finalPrompt += `涉及的设备厂商为${brand}。`
            }
            if (eqp && eqp != '不限') {
                finalPrompt += `涉及的设备类型为${eqp}。`
            }
            finalPrompt += '回答内容仅包括配置思路和配置步骤两个部分。配置思路和配置步骤两个标题，请加粗，并在前面加一个"☑ "符号'
        } else if (currentAssistantMode == assistanModeEnum.ASSISTANT_DEBUG || (currentAssistantMode == assistanModeEnum.MULTI_AGENT && mutiAgentSubAssistantModeState == assistanModeEnum.ASSISTANT_DEBUG)) {
            finalPrompt = "你的身份是联通的云池数通设备辅助调试助手，请根据用户输入的内容或配置，调试它，并试图找到其中的问题，并给出解决方案。如果没有问题，则不需要给出解决方案。"
        } else if (currentAssistantMode == assistanModeEnum.CONFIG_GENERATOR || (currentAssistantMode == assistanModeEnum.MULTI_AGENT && mutiAgentSubAssistantModeState == assistanModeEnum.CONFIG_GENERATOR)) {
            finalPrompt = "你的身份是联通的云池数通设备配置生成助手，请根据用户输入的内容或配置思路，输出配置代码。你的回答中仅包含整体代码块。在回答的最后增加一些总结。"

        } else if (currentAssistantMode == assistanModeEnum.TOPO_ANAYLSIS || (currentAssistantMode == assistanModeEnum.MULTI_AGENT && mutiAgentSubAssistantModeState == assistanModeEnum.TOPO_ANAYLSIS)) {
            finalPrompt = "你的身份是联通的云池数通设备拓扑分析助手，你需要帮助用户完成拓扑图分析。分析的维度包括：设备信息，连接关系，技术实现等。如果用户上传的不是网络拓扑图，请拒绝回答，并告知客户你的能力和定位"
        }
        console.log('提示词：' + finalPrompt)
        return finalPrompt
    }
    // let qaid = useRef('')

    //=====================dlckendstate==================

    // ==================== State ====================
    useEffect(() => {
        document.title = "云池数通设备配置助手"
        // console.log(QAProps.assistantMode)
        if (QAProps.assistantMode) {
            console.log('来自内部组件调用')
            placeholderRef.current?.onPromptsItemClick(QAProps.assistantMode)
        }
    }, [])

    // ==================== Runtime ====================
    interface Message {
        id?: string,
        event?: string,
        data: string
    }
    const [agent] = useXAgent<AgentMessage>({
        request: async ({ message }, { onSuccess, onUpdate }) => {
            isRefreshList.current = true
            try {
                let megList: string = ''
                let searchResults: [] = []
                const qaid = message?.qaid || ''
                const subAssistantMode = mutiAgentSubAssistantMode.current

                onUpdate({
                    role: "ai",
                    content: [searchResults, "loading"],
                    isfavourite: 0,
                })

                let modelMode = MODEL_MODE.LLMS
                if (
                    message &&
                    (message.content as RequestInfo).files.length > 0 &&
                    (message.content as RequestInfo).files[0].type === 'image'
                ) {
                    modelMode = MODEL_MODE.MMLLMS
                }

                let requestBody = {
                    stream: "true",
                    api_key: "sk-a05ff16ef8414a45a75db780ecd11d67",
                    model: ''
                }

                let callBackObj = {
                    onSuccess: () => {
                        // console.log('onSuccess--->', megList);
                        // console.log(typeof (qaid.current))
                        // addMessageAnswer(megList, qaid.current)
                        onSuccess({
                            role: "ai",
                            content: [searchResults, megList, true, qaid, subAssistantMode],
                            qaid: qaid,
                            isfavourite: 0
                        })
                    },
                    onUpdate: (messages: Message) => {
                        if (modelMode == MODEL_MODE.MMLLMS) {
                            // 对于多模态模型，检查是否是内部模型
                            // if (modelConfigRef.current.id === 'inner') {
                            //     // 内部多模态模型使用外部模型的解析逻辑
                            //     const result = MODEL_SOURCES.OUT.parseResponse(messages.data, searchResults);
                            //     megList += result.content;
                            //     if (result.searchResults) {
                            //         searchResults = result.searchResults as [];
                            //     }
                            // } else {
                            // 外部多模态模型使用原有逻辑
                            let msgData = JSON.parse(messages.data)
                            megList += msgData?.choices[0]?.delta?.content || '';
                            // }
                        } else if (modelMode == MODEL_MODE.LLMS) {
                            // 不需要在这里解析，使用配置中的解析方法

                            // 使用 modelConfigRef 中的模型配置
                            console.log("正在使用模型配置解析响应:", modelConfigRef.current.id);

                            // 使用 modelConfigRef 中的配置解析响应
                            const result = modelConfigRef.current.parseResponse(messages.data, searchResults);
                            megList += result.content;
                            if (result.searchResults) {
                                searchResults = result.searchResults as [];
                            }
                        }

                        onUpdate({
                            role: "ai",
                            content: [searchResults, megList],
                            qaid: qaid,
                            isfavourite: 0,
                        })
                    },
                    onError: (error: any) => {
                        console.error('onError--->', error);
                        let msg = error.message
                        if (error.message == 'signal is aborted without reason' || error.message == 'BodyStreamBuffer was aborted') {
                            msg = "用户中断输出"
                        }
                        megList += `\n\n<span style="color: red;">${msg}</span>`
                        onSuccess({
                            role: "ai",
                            content: [searchResults, megList, false],
                            qaid: qaid,
                            isfavourite: 0
                        })
                    },
                }

                if (modelMode == MODEL_MODE.LLMS) {
                    message!.content = message!.content as RequestInfo
                    //如果用户上传了excel的附件，需要解析出来放入大模型的提示词
                    let excelPrompt = ''
                    if (message &&
                        (message.content as RequestInfo).files.length > 0 &&
                        (message.content as RequestInfo).files[0].type === 'excel') {
                        excelPrompt = '请根据附件中的数据，理解分析，并根据用户的需要进行配置生成。\n\n注意，你生成的配置，格式必须是网络设备可以直接复制使用的配置格式，而用户提供的配置的是附件JSON的格式。\n\n配置生成的范围不要超过用户提供的附件的内容范围，除非用户主动要求。\n\n参考资料仅作为代码生成的语法和命令参考，请不要将参考资料作为代码生成的结果。'
                    }

                    // 使用 modelConfigRef 中的模型配置
                    console.log("正在使用模型配置格式化请求:", modelConfigRef.current.id, "模型ID:", selectedModelRef.current);

                    // 将选定的模型ID添加到请求体中
                    if (modelConfigRef.current.id === 'inner') {
                        requestBody.model = selectedModelRef.current;
                    }

                    // 使用 modelConfigRef 中的配置格式化请求
                    requestBody = modelConfigRef.current.formatRequest(
                        requestBody,
                        modelMode,
                        message,
                        brandEqpPrompt.current,
                        excelPrompt,
                        isInternetSearch.current
                    );
                    console.log("格式化后的请求体:", requestBody);
                } else if (modelMode == MODEL_MODE.MMLLMS) {
                    // const urls: {}[] = [];
                    // (message && message.content as RequestInfo)?.files?.forEach((img: any) => {
                    //     urls.push({
                    //         type: "image_url",
                    //         image_url: {
                    //             url: img.data
                    //         }
                    //     })
                    // })
                    // // uploadRef.current?.clearFiles()
                    // requestBody = Object.assign(requestBody, {
                    //     model: "qwen-vl-max-latest",
                    //     q: [{
                    //         role: "system", content: [{ type: "text", text: brandEqpPrompt.current }]
                    //     },
                    //     {
                    //         role: "user", content: [...urls, { type: "text", text: message && (message.content as RequestInfo).question }]
                    //     }],
                    // })
                    requestBody = modelConfigRef.current.formatRequest(
                        requestBody,
                        modelMode,
                        message,
                        brandEqpPrompt.current,
                        '',
                        isInternetSearch.current
                    );
                }
                uploadRef.current?.clearFiles()

                await requestRef.current.create(requestBody as Object, callBackObj)

            } catch (error) {
                console.log('error', error);
            }

        }
    });


    const { onRequest, messages, setMessages } = useXChat<AgentMessage>({
        agent,
    });

    useEffect(() => {
        // console.log(messages)
        setRawMessageList(messages, QAProps.conversationId)

    }, [messages])

    const [controller, setController] = useState<AbortController | null>(null)
    // 使用useRef存储XRequest实例，这样我们可以在useEffect中更新它
    const requestRef = useRef<any>(null);

    // 当模型配置变化时重新创建XRequest实例
    useEffect(() => {
        requestRef.current = XRequest({
            baseURL: modelConfigRef.current.baseURL,
            fetch: async (baseURL, options) => {
                delete options?.headers
                if (controller) {
                    controller.abort()
                }
                //没有的话就到这一步
                const sl = new AbortController()
                setController(() => sl)

                // 检查是否是内部多模态模型，如果是则使用外部模型的配置
                let actualBaseURL = baseURL;
                let actualHeaders = modelConfigRef.current.headers;

                // 检查当前请求是否为多模态模式且使用内部模型
                const isInternalMultimodal = modelConfigRef.current.id === 'inner' &&
                    options?.body &&
                    typeof options.body === 'string' &&
                    JSON.parse(options.body).q; // 检查是否包含多模态请求格式

                if (isInternalMultimodal) {
                    // 对于内部多模态模型，使用外部模型的baseURL和headers
                    actualBaseURL = MODEL_SOURCES.OUT.baseURL;
                    actualHeaders = MODEL_SOURCES.OUT.headers;
                    console.log("内部多模态模型，切换到外部模型配置");
                }

                console.log("发送请求到:", actualBaseURL);
                console.log("使用模型配置:", modelConfigRef.current.id);
                console.log("使用模型ID:", selectedModelRef.current);
                console.log("是否为内部多模态:", isInternalMultimodal);

                const response = await fetch(actualBaseURL, {
                    ...options,
                    signal: sl.signal,
                    headers: actualHeaders
                })
                return response
            }
        });
    }, [selectedModel, controller]);

    const onRAGRequest = (query: string | RequestInfo) => {
        if (typeof query == 'string') {
            query = {
                question: query,
                files: uploadedFiles
            }
        }
        console.log(query)
        // 请求参数
        const data = {
            appId: "RxcQ0MrXwOpBf",
            topK: topKRef.current,
            categoryId: 127,
            // categoryId: 71
            query: query.files.length ? (query.question + '\n\n附件内容为:' + '\n\n' + query.files.map((item) => JSON.stringify(item.data)).join('\n\n')) : query.question, // 替换为实际的查询内容
            similarity: similarityRef.current
        };
        let rag = '';
        fetch(API_BASE_URL + '/config-rag' + '/CUCCAI-intelligent-agent/vectorSearchApi/', {
            method: 'POST', // 请求方法
            headers: {
                'Content-Type': 'application/json' // 设置请求头，指定发送JSON格式
            },
            body: JSON.stringify(data) // 将请求体转换为JSON字符串
        })
            .then((response) => {
                // 检查响应是否成功
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json(); // 解析JSON数据
            })
            .then((data) => {
                // console.log('data-->', data);
                if (data?.data?.segments?.length) {
                    rag += "\n\n参考资料: "
                    data.data.segments.forEach((item: { type: number, content: string }) => {
                        if (item.type == 2) {
                            rag += item.content
                        }
                    })
                }
            })
            .catch((error) => {
                console.log('error-->', error);
            }).finally(() => {
                // qaid.current = addMessageQuestion(query, QAProps.conversationId)
                onRequest({
                    role: "local",
                    system: rag,
                    content: query,
                    qaid: uuidv4(),
                    isfavourite: 0
                });
            });

    }
    // ==================== Event ====================
    const onSubmit = (nextContent: string) => {
        if (!nextContent) return;
        onRAGRequest(nextContent)
        setContent('');
    };
    const onReLoad = () => {
        if (messages.length >= 2) {
            const q = messages[messages.length - 2]
            // setImages(() => (q.message.content as RequestInfo).images ?? [])
            tempMessageListBeforeDelete.current = messages
            isRefreshList.current = false
            setMessages(msgs => msgs.slice(0, -2))
            onRAGRequest((q.message.content as RequestInfo))
        }
    }

    const modelRender = (data: ModelData | string): React.ReactNode => {
        if (typeof data === "string") {
            return <>{data}</>
        }
        return (
            <details open>
                <summary className={['cursor-pointer font-bold text-blue-700 text-base select-none marker:text-gray-700', styles.summary_style].join(" ")}><span className='mr-2'>{data.title}</span></summary>
                <p className='mt-3'>{data.desc}</p>
                <p className='text-blue-500 font-bold mt-3 mb-2'>您可以尝试提问，体验我的超能力:</p>
                {
                    data.questionList && data.questionList.map((item, index) => {
                        return <p className='underline cursor-pointer mb-1' key={index} onClick={() => {
                            onRAGRequest(item)
                        }}>{index + 1}、 {item}</p>
                    })
                }
            </details>

        )
    }

    const onNextAgentClick = (assistMode: string, answer: string) => {
        setMutiAgentSubAssistantModeState(assistMode)
        onRAGRequest(answer)
    }

    const roles: GetProp<typeof Bubble.List, 'roles'> = {
        ai: {
            placement: 'start',
            avatar: {
                icon: <img src={Robot} />, style: {
                    background: 'transparent', maxWidth: "45px", maxHeight: "45px", width: '45px', height: "45px"
                }
            },
            styles: {
                content: {
                    borderRadius: 16,
                    background: "#ffffff"
                },
                footer: {
                    marginTop: 3
                }
            },
            messageRender: (content: any) => <AiResult content={content} onFavouriteChange={onFavouriteChange} assistantMode={currentAssistantMode} onNextAgentClick={onNextAgentClick} />
        },
        local: {
            placement: 'end',
            variant: 'shadow',
            styles: {
                content: {
                    background: "#ffffff"
                }
            },
            messageRender: (content: RequestInfo | string) => {
                // print(content)

                if (content !== null && typeof content !== "string" && content && "question" in content && content.files.length) {
                    if (content.files[0].type === 'image') {
                        content = content as RequestInfo
                        return <>
                            <p className='text-end'>{content.question}</p>
                            <Flex vertical gap="middle" align='end'>
                                {content.files && (content.files).map((item, index) => (
                                    <Image width={200} height={200} src={item.data} key={index}></Image>
                                ))}
                            </Flex>
                        </>
                    } else if (content.files[0].type === 'excel') {
                        return <>
                            {
                                content.files.map((file, index) => {
                                    // print('file-->' + file)
                                    return <Attachments.FileCard key={index} item={file.rawData} />
                                })
                            }
                            <p className='text-end mt-1'>{(content as RequestInfo).question}</p>
                        </>
                    }
                } else {
                    return <span>{(content as RequestInfo).question}</span>
                }
            }
        },
        model: {
            placement: 'start',
            avatar: {
                icon: <img src={Robot} />, style: {
                    background: 'transparent', maxWidth: "45px", maxHeight: "45px", width: '45px', height: "45px"
                }
            },
            styles: {
                content: {
                    borderRadius: 16,
                    background: "#ffffff"
                },
            },
            messageRender: modelRender
        }
    };

    const onFavouriteChange = (qaid: string, isfavourite: number) => {


        for (let j = 0; j < messages.length; j++) {
            let message = messages[j];

            if (message.message.qaid === qaid && message.message.role === 'ai') {
                message.message.isfavourite = isfavourite
                // console.log('修改收藏状态', message);
                break;
            }

        }
        setRawMessageList(messages, QAProps.conversationId, true)

    }

    // ==================== Nodes ====================
    const items: GetProp<typeof Bubble.List, 'items'> = React.useMemo(() => {

        let bubbleList = []
        if (!isRefreshList.current) {
            bubbleList = tempMessageListBeforeDelete.current
        } else {
            bubbleList = messages
        }

        return bubbleList.map(({ id, message, status }, index) => {
            return {
                key: id,
                ...message,
                footer: (index == bubbleList.length - 1 && message.role == "ai" && status != "loading") ? <SyncOutlined className='cursor-pointer' onClick={onReLoad} /> : <></>
            }
        });
    }, [messages]);
    // console.log(QAProps.isActive)

    // ==================== Render =================
    return (
        <div style={{ display: !QAProps.isActive ? 'none' : '' }} className={` ${styles.layout} w-full`}>
            <div className={styles.chat}>
                {currentAssistantMode === assistanModeEnum.MULTI_AGENT && (
                    <div className="w-full h-[200px] border border-[#ddd] rounded-lg">
                        <ReactFlow
                            nodes={nodes as any}
                            edges={edges as any}
                            onConnect={onConnect}
                            defaultEdgeOptions={defaultEdgeOptions as any}
                            fitView
                        >
                            <Background />
                            {/* <Controls /> */}

                        </ReactFlow>
                    </div>
                )}
                {/* 🌟 欢迎页占位 */}

                <div className={QAProps.assistantMode ? 'hidden' : ''}>
                    {items.length == 0 && <Placeholder ref={placeholderRef} setCurrentAssistantMode={setCurrentAssistantMode} setMessages={setMessages} />}
                </div>
                {/* 🌟 消息列表 */}
                <Bubble.List
                    items={items}
                    roles={roles}
                    className={styles.messages + ' scroll-smooth'}

                />
                {/* 🌟 输入框 */}
                {
                    currentAssistantMode == assistanModeEnum.CONFIG_RECOMMEND ?
                        <div>
                            <div className='selector_container'>
                                <span className='ml-3'>厂商</span>
                                <Select
                                    // size='large'
                                    defaultValue="不限"
                                    style={{ width: 120, marginLeft: 10 }}
                                    className='no-border'
                                    onChange={brandChange}
                                    options={brand} />
                            </div>
                            <div className="selector_container ml-3">
                                <span className='ml-3'>设备</span>
                                <Select
                                    // size='large'
                                    defaultValue="不限"
                                    style={{ width: 120, marginLeft: 10 }}
                                    className='no-border'
                                    onChange={equipmentChange}
                                    options={equipment} />
                            </div>
                        </div> : ''
                }
                {
                    currentAssistantMode == assistanModeEnum.MULTI_AGENT &&
                    <div className='selector_container w-fit'>
                        <span className='ml-3'>当前助手</span>
                        <Select
                            // size='large'
                            value={mutiAgentSubAssistantModeState}
                            // defaultValue={mutiAgentSubAssistantModeState}
                            className='no-border w-[120] ml-1'
                            onChange={mutiAgentSubAssistantModeChange}
                            options={RichAssistantModeEnum.map((e) => {
                                return {
                                    label: e.rawName,
                                    value: e.key,
                                    id: e.key
                                }
                            }).filter((e) => e.id !== assistanModeEnum.MULTI_AGENT)} />
                    </div>
                }
                {/* Model Selection */}

                <div>
                    <div className='selector_container mr-3 w-fit'>
                        <span className='ml-3'>模型选择</span>
                        <Select
                            value={selectedModel}
                            style={{ width: 200, marginLeft: 10 }}
                            className='no-border w-min'
                            onChange={(value) => {
                                console.log("切换模型为:", value);
                                setSelectedModel(value);
                            }}
                            options={MODELS.map(model => ({
                                label: model.name,
                                value: model.id,
                                disabled: !model.avaliable
                            }))}
                        />
                    </div>

                    {/* TopK Input */}
                    <div className='selector_container mr-3 w-fit'>
                        <span className='ml-3'>TopK</span>
                        <InputNumber
                            value={topK}
                            style={{ width: 80, marginLeft: 10 }}
                            className='no-border'
                            min={1}
                            max={20}
                            onChange={(value) => {
                                if (value !== null) {
                                    setTopK(value);
                                    console.log("TopK 已更新为:", value);
                                }
                            }}
                        />
                    </div>

                    {/* Similarity Input */}
                    <div className='selector_container mr-3 w-fit'>
                        <span className='ml-3'>相似度</span>
                        <InputNumber
                            value={similarity}
                            style={{ width: 80, marginLeft: 10 }}
                            className='no-border'
                            min={0}
                            max={1}
                            step={0.01}
                            precision={2}
                            onChange={(value) => {
                                if (value !== null) {
                                    setSimilarity(value);
                                    console.log("相似度已更新为:", value);
                                }
                            }}
                        />
                    </div>
                </div>



                <div className={styles.sender_container}>
                    <Sender
                        header={<UploadFile ref={uploadRef} onFileChange={handleFileUpload} handleFile={handleFile} supportFormat={['jpg', 'jpeg', 'xlsx', 'xls', 'png']} />}
                        prefix={
                            ((currentAssistantMode === assistanModeEnum.TOPO_ANAYLSIS) ||
                                (currentAssistantMode === assistanModeEnum.MULTI_AGENT) ||
                                (currentAssistantMode === assistanModeEnum.CONFIG_GENERATOR) ||
                                (currentAssistantMode === assistanModeEnum.CONFIG_RECOMMEND) ||
                                (currentAssistantMode === assistanModeEnum.ASSISTANT_DEBUG)
                            )

                            &&
                            <Button
                                type="text"
                                icon={<LinkOutlined />}
                                onClick={() => {
                                    uploadRef.current?.btnClick()
                                }}
                            >
                            </Button>
                        }
                        value={content}
                        onSubmit={onSubmit}
                        onChange={setContent}
                        loading={agent.isRequesting()}
                        className={styles.sender}
                        onCancel={() => controller?.abort()}
                    />
                    <div className="flex items-center">


                        {/* Internet Search Button */}
                        {((currentAssistantMode !== assistanModeEnum.TOPO_ANAYLSIS && currentAssistantMode !== assistanModeEnum.MULTI_AGENT) ||
                            (currentAssistantMode === assistanModeEnum.MULTI_AGENT && mutiAgentSubAssistantModeState !== assistanModeEnum.TOPO_ANAYLSIS)) ?
                            <SelectButton className={`internet_button`} text="联网搜索" onSelectionChange={(is) => isInternetSearch.current = is} /> :
                            <div className='internet_button'></div>
                        }
                    </div>
                </div>
            </div>

        </div>
    );
};

export default Independent;
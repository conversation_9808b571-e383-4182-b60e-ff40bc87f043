import { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import './index.css'

export default function Index() {
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const kgtoken = new URLSearchParams(location.search).get('kgtoken');
    const iframeUrl = `http://10.186.2.176:10012/uat-new-graph/chatui/#/knowledge/home?kgtoken=${kgtoken}`

    const handleLoad = () => {
        console.log('iframe加载完成');
        setIsLoading(false);
    };

    const handleError = () => {
        console.error('iframe加载失败');
        setIsLoading(false);
    };

    useEffect(() => {
        const iframe = iframeRef.current;
        if (iframe) {
            iframe.addEventListener('load', handleLoad);
            iframe.addEventListener('error', handleError);
        }

        // 组件卸载时清理
        return () => {
            if (iframeRef.current) {
                iframeRef.current.removeEventListener('load', handleLoad);
                iframeRef.current.removeEventListener('error', handleError);
            }
        };
    }, []);

    return (
        <div style={{ width: "100%", height: "100%" }}>
            <iframe
                src={iframeUrl}
                width="100%"
                height="100%"
                ref={iframeRef}
                onLoad={handleLoad}
                onError={handleError}
                style={isLoading ? { visibility: 'hidden' } : {}}
            ></iframe>
            {isLoading ? <div className="loading-indicator loader" ></div> : null}
        </div>
    );
}
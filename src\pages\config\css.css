.top_bar_container {
    background-color: #0150bf;
    height: 55px;
}

.flex_container {
    flex: 1;
}

.flex-column_container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-width: 960px;
}

.top_bar_container {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.top_bar_logo {
    height: 30px;
}

.top_bar_title {
    font-size: 25px;
    font-weight: 600;
    color: white;
    letter-spacing: 3px;
}

.top_bar_rag {
    margin-left: auto;
    margin-right: 15px;
    color: white;
}

.top_bar_user_avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.top_bar_user_name {
    font-size: 20px;
    font-weight: 600;
    color: white;

}

.margin-row {
    margin-left: 10px;
    margin-right: 10px;
}

.flex-auto_container {
    flex: auto;
    overflow: hidden;
    display: flex;
}


.conversations {
    padding: 0 12px;
    flex: 1;
    overflow-y: auto;
}

.addBtn {
    background: #1677ff0f;
    border: 1px solid #1677ff34;

    box-sizing: border-box;
    margin: 10px;
}

.menu {
    min-width: 250px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background: #dae1ec;
}


.no-border>.ant-select-selector {
    border: none !important;
}

/* .ant-select-selector {
    border: none !important;
} */

.selector_container {
    background: white;
    display: inline-block;
    border-radius: 5px;
    font-size: 14px;
    color: #b5b9bc;
    box-sizing: border-box;
    padding-top: 3px;
    padding-bottom: 3px;
    border: 1px solid #d9d9d9
}

.selector_container>span {
    margin-bottom: 3px;
}

.show {
    display: block;
}


.flex_row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex-half {
    flex: 1 1 50%;
}

.circle {
    border-radius: 50%;
    border: 1px solid lightgrey;
    background: white;

}

.square {
    width: 20px;
    height: 20px;
}

.bxgxiv {
    max-width: initial !important;
}
import { BrowserRouter } from 'react-router-dom';
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { ConfigProvider } from 'antd';
import zh_CN from 'antd/es/locale/zh_CN';

createRoot(document.getElementById('root')!).render(
  <BrowserRouter future={{
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }}>
    <ConfigProvider locale={zh_CN}>
      <App />
    </ConfigProvider>
  </BrowserRouter>
)

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

iframe {
    border: none;
    display: block;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}


/* HTML: <div class="loader"></div> */
.loader {
    width: fit-content;
    font-size: 40px;
    font-family: system-ui,sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    color: #0000;
    -webkit-text-stroke: 1px #000;
    background:
      radial-gradient(0.71em at 50% 1em,#000 99%,#0000 101%) calc(50% - 1em) 1em/2em 200% repeat-x text,
      radial-gradient(0.71em at 50% -0.5em,#0000 99%,#000 101%) 50% 1.5em/2em 200% repeat-x text;
    animation: 
      l10-0 .8s linear infinite alternate,
      l10-1  4s linear infinite;
  }
  .loader:before {
    content: "加载中...";
  }
  @keyframes l10-0 {
    to {background-position-x: 50%,calc(50% + 1em)}
  }
  @keyframes l10-1 {
    to {background-position-y: -.5em,0}
  }